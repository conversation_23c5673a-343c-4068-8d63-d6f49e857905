<template>
  <div :id="id" :style="{width: width, height: height}"></div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: '<PERSON><PERSON><PERSON>',
  mixins: [resize],
  props: {
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    dataList: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          textStyle: {
            color: '#fff'
          },
          data: this.dataList.map(item => item.name)
        },
        series: [
          {
            name: '用户分析',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold',
                color: '#fff'
              }
            },
            labelLine: {
              show: false
            },
            data: this.dataList.map(item => {
              return {
                value: item.value,
                name: item.name
              }
            }),
            itemStyle: {
              normal: {
                color: function(params) {
                  const colorList = ['#4d88ff', '#36cbcb', '#975fe5', '#FF9985', '#fbd258']
                  return colorList[params.dataIndex % colorList.length]
                }
              }
            }
          }
        ]
      }
      
      this.chart.setOption(option)
    }
  }
}
</script> 