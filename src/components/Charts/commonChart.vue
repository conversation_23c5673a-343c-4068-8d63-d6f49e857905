<template>
  <div :id="id" :style="{width: width, height: height}"></div>
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

export default {
  name: 'CommonC<PERSON>',
  mixins: [resize],
  props: {
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    title: {
      type: String,
      default: ''
    },
    chartType: {
      type: String,
      default: 'bar'
    },
    color: {
      type: Array,
      default: () => ['#fc5454']
    },
    headList: {
      type: Array,
      default: () => []
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    dataList: {
      deep: true,
      handler(val) {
        this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))
      const xData = this.dataList.map(item => item.name)
      const seriesData = this.dataList.map(item => item.value)
      
      // 配置项
      let option = {
        title: {
          text: this.title,
          textStyle: {
            fontSize: 14,
            color: '#fff'
          },
          left: 'center',
          top: 10
        },
        color: this.color,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,.1)'
            }
          },
          axisLabel: {
            color: 'rgba(255,255,255,.7)',
            fontSize: 12
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,.1)'
            }
          },
          axisLabel: {
            color: 'rgba(255,255,255,.7)',
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,.1)'
            }
          }
        },
        series: []
      }
      
      // 根据图表类型配置系列
      if (this.chartType === 'bar') {
        option.series.push({
          name: this.headList[1] || '',
          type: 'bar',
          barWidth: '35%',
          data: seriesData,
          itemStyle: {
            normal: {
              barBorderRadius: 5,
              color: function(params) {
                return new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: params.dataIndex === 0 ? '#fc5454' : '#fc5454' // 开始颜色
                }, {
                  offset: 1,
                  color: params.dataIndex === 0 ? '#ff9985' : '#ff9985' // 结束颜色
                }], false)
              }
            }
          }
        })
      } else if (this.chartType === 'line') {
        option.series.push({
          name: this.headList[1] || '',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 5,
          showSymbol: false,
          lineStyle: {
            normal: {
              width: 2
            }
          },
          itemStyle: {
            normal: {
              color: this.color[0],
              borderColor: this.color[0],
              borderWidth: 2
            }
          },
          data: seriesData
        })
      }
      
      this.chart.setOption(option)
    }
  }
}
</script> 