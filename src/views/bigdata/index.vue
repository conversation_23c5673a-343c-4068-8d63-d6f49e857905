<template>
  <div class="bigdata-container">
    <!-- 加载动画 -->
    <div class="loading" v-if="loading">
      <div class="loadbox">
        <img src="@/assets/images/loading.gif" /> 页面加载中...
      </div>
    </div>

    <!-- 头部 -->
    <div class="head">
      <el-button
        type="text"
        class="back-btn"
        @click="goBack"
        icon="el-icon-arrow-left"
      >
        返回数据看板
      </el-button>
      <h1>大数据可视化系统数据分析通用模版</h1>
      <div class="time" id="showTime">
        {{ currentTime }}
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="mainbox">
      <ul class="clearfix">
        <!-- 左侧列 -->
        <li>
          <div class="boxall" style="height: calc(58% - .15rem)">
            <div class="alltitle">
              近七日订单数量
            </div>
            <div class="boxnav" id="echarts4">
              <commonChart 
                v-if="chartData1.length > 0" 
                :title="chart1.title" 
                :color="chart1.color" 
                :chart-type="chart1.chartType" 
                :head-list="chart1.header" 
                :data-list="chartData1" 
                width="100%" 
                id="chart4" 
                height="100%"
              />
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              近七日会员活跃数
            </div>
            <div class="boxnav" id="echarts3">
              <commonChart 
                v-if="chartData2.length > 0" 
                :title="chart2.title" 
                :color="chart2.color" 
                :chart-type="chart2.chartType" 
                :head-list="chart2.header" 
                :data-list="chartData2" 
                width="100%" 
                id="chart3" 
                height="100%"
              />
            </div>
          </div>
        </li>

        <!-- 中间列 -->
        <li>
          <div class="boxall" style="height: calc(20% - .15rem)">
            <ul class="row h100 clearfix">
              <li class="col-6">
                <div class="sqzs h100">
                  <p>业绩总览</p>
                  <h1><span>{{ formatNumber(homeData.totalPay) }}</span>万</h1>
                </div>
              </li>
              <li class="col-6">
                <ul class="row h100 clearfix">
                  <li class="col-4">
                    <div class="tit01">
                      标题名称
                    </div>
                    <div class="piebox" id="pe01">
                      <div class="pie-number">{{ homeData.todayOrder }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      标题名称
                    </div>
                    <div class="piebox" id="pe02">
                      <div class="pie-number">{{ homeData.todayUser }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      标题名称
                    </div>
                    <div class="piebox" id="pe03">
                      <div class="pie-number">{{ homeData.todayActiveUser }}</div>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
          <div class="boxall" style="height: calc(38% - .15rem)">
            <div class="alltitle">
              标题名称
            </div>
            <div class="boxnav" id="echarts1">
              <gradientChart 
                v-if="chartData1.length > 0" 
                :title="chart1.title" 
                :data-list="chartData1" 
                width="100%" 
                id="chart1" 
                height="100%"
              />
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              交易金额趋势
            </div>
            <div class="boxnav" id="echarts2">
              <commonChart 
                v-if="paymentData.length > 0" 
                :title="paymentChart.title" 
                :color="paymentChart.color" 
                :chart-type="paymentChart.chartType" 
                :head-list="paymentChart.header" 
                :data-list="paymentData" 
                width="100%" 
                id="chart2" 
                height="100%"
              />
            </div>
          </div>
        </li>

        <!-- 右侧列 -->
        <li>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              数据分布
            </div>
            <div class="boxnav" id="echarts5">
              <commonChart 
                v-if="distributionData.length > 0" 
                :title="distributionChart.title" 
                :color="distributionChart.color" 
                :chart-type="distributionChart.chartType" 
                :head-list="distributionChart.header" 
                :data-list="distributionData" 
                width="100%" 
                id="chart5" 
                height="100%"
              />
            </div>
          </div>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              标题名称
            </div>
            <div class="boxnav">
              <table border="0" cellspacing="0">
                <tbody>
                <tr>
                  <th></th>
                  <th>字段</th>
                  <th>字段</th>
                  <th>字段</th>
                  <th>字段</th>
                </tr>
                <tr>
                  <th>字段</th>
                  <td>{{ homeData.todayOrder || 8098 }}</td>
                  <td>19.80%</td>
                  <td>22</td>
                  <td>368</td>
                </tr>
                <tr>
                  <th>字段</th>
                  <td>{{ yesterdayData.order || 7506 }}</td>
                  <td>6.70%</td>
                  <td>22</td>
                  <td>339</td>
                </tr>
                <tr>
                  <th>字段</th>
                  <td>{{ monthData.order || 3261 }}</td>
                  <td>32.30%</td>
                  <td>10</td>
                  <td>325.7</td>
                </tr>
                <tr>
                  <th>字段</th>
                  <td>{{ homeData.totalOrder || 1993 }}</td>
                  <td>201%</td>
                  <td>10</td>
                  <td>199</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              用户分析
            </div>
            <div class="boxnav" id="echarts6" style="height:calc(100% - .3rem);">
              <pie-chart
                v-if="userData.length > 0"
                :data-list="userData"
                width="100%"
                id="chart6"
                height="100%"
              />
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getHomeData, getStatisticsData } from '@/api/dashboard'
import commonChart from '@/components/Charts/commonChart'
import gradientChart from '@/components/Charts/gradientChart'
import pieChart from '@/components/Charts/pieChart'

export default {
  name: 'BigData',
  components: {
    commonChart,
    gradientChart,
    pieChart
  },
  data() {
    return {
      loading: true,
      currentTime: '',
      homeData: {
        todayOrder: 0,
        todayPay: 0,
        todayUser: 0,
        todayActiveUser: 0,
        totalOrder: 0,
        totalPay: 0,
        totalUser: 0
      },
      yesterdayData: {
        order: 0,
        pay: 0,
        user: 0
      },
      monthData: {
        order: 0,
        pay: 0,
        user: 0
      },
      chartData1: [],
      chartData2: [],
      paymentData: [],
      distributionData: [],
      userData: [],
      chart1: {
        title: '近七日订单数量',
        color: ['#fc5454'],
        chartType: 'bar',
        header: ['日期', '订单数量']
      },
      chart2: {
        title: '近七日会员活跃数',
        color: ['#2ec7c9'],
        chartType: 'line',
        header: ['日期', '会员活跃数']
      },
      paymentChart: {
        title: '交易金额趋势',
        color: ['#ffb980'],
        chartType: 'line',
        header: ['月份', '交易金额']
      },
      distributionChart: {
        title: '数据分布',
        color: ['#5ab1ef', '#b6a2de', '#67e0e3', '#ffb980', '#2ec7c9'],
        chartType: 'bar',
        header: ['商品', '分布统计']
      }
    }
  },
  created() {
    this.timer = setInterval(() => {
      this.currentTime = this.formatDate(new Date())
    }, 1000)
    this.getHomeData()
    this.getStatisticsData()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    goBack() {
      this.$router.push('/dashboard')
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = this.addZero(date.getMonth() + 1)
      const day = this.addZero(date.getDate())
      const hour = this.addZero(date.getHours())
      const minute = this.addZero(date.getMinutes())
      const second = this.addZero(date.getSeconds())
      return `${year}/${month}/${day} ${hour}:${minute}:${second}`
    },
    addZero(num) {
      return num < 10 ? '0' + num : num
    },
    formatNumber(num) {
      if (!num) return 0
      return (num / 10000).toFixed(2)
    },
    async getHomeData() {
      try {
        const res = await getHomeData()
        if (res.code === 0) {
          this.homeData = res.data
          this.loading = false
        }
      } catch (error) {
        console.error('获取首页数据失败', error)
        this.loading = false
      }
    },
    async getStatisticsData() {
      try {
        const res = await getStatisticsData()
        if (res.code === 0) {
          const { orderData, userData, paymentData, productData, userTypeData } = res.data
          
          // 处理订单数据
          this.chartData1 = orderData.map(item => ({
            name: item.date,
            value: item.count
          }))
          
          // 处理会员活跃数据
          this.chartData2 = userData.map(item => ({
            name: item.date,
            value: item.count
          }))
          
          // 处理支付数据
          this.paymentData = paymentData.map(item => ({
            name: item.month,
            value: item.amount
          }))
          
          // 处理商品分布数据
          this.distributionData = productData.map(item => ({
            name: item.name,
            value: item.count
          }))
          
          // 处理用户类型数据
          this.userData = userTypeData.map(item => ({
            name: item.type,
            value: item.count
          }))
          
          // 处理昨日和本月数据
          if (orderData.length > 1) {
            this.yesterdayData.order = orderData[orderData.length - 2].count
          }
          
          // 模拟数据
          this.yesterdayData = {
            order: 25,
            pay: 32339,
            user: 0
          }
          
          this.monthData = {
            order: 120,
            pay: 56486,
            user: 2
          }
        }
      } catch (error) {
        console.error('获取统计数据失败', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import url('../../assets/css/common.css');

.bigdata-container {
  width: 100%;
  height: 100%;
  background: #000d4a url(../../assets/images/bg.jpg) center center;
  background-size: cover;
  color: #fff;
  font-size: 16px;
  overflow: hidden;
}

.loading {
  position: fixed;
  left: 0;
  top: 0;
  font-size: 16px;
  z-index: 100000000;
  width: 100%;
  height: 100%;
  background: #1a1a1c;
  text-align: center;
}

.loadbox {
  position: absolute;
  width: 160px;
  height: 150px;
  color: #324e93;
  left: 50%;
  top: 50%;
  margin-top: -100px;
  margin-left: -75px;
}

.loadbox img {
  margin: 10px auto;
  display: block;
  width: 40px;
}

.head {
  height: 80px;
  background: url(../../assets/images/head_bg.png) no-repeat center center;
  background-size: 100% 100%;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 20px;
  top: 20px;
  color: #fff;
  font-size: 14px;
}

.head h1 {
  color: #fff;
  text-align: center;
  font-size: 32px;
  line-height: 80px;
  letter-spacing: -1px;
}

.time {
  position: absolute;
  right: 30px;
  top: 0;
  line-height: 80px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 20px;
  font-family: "DS-DIGIT";
}

.mainbox {
  padding: 0 10px 0 10px;
  height: calc(100% - 80px);
}

.mainbox > ul {
  margin-left: -10px;
  margin-right: -10px;
  height: 100%;
}

.mainbox > ul > li {
  float: left;
  padding: 0 10px;
  height: 100%;
  width: 30%;
}

.mainbox > ul > li:nth-child(2) {
  width: 40%;
}

.boxall {
  padding: 0 10px 10px 10px;
  background: rgba(6, 48, 109, 0.5);
  position: relative;
  margin-bottom: 15px;
  z-index: 10;
}

.alltitle {
  font-size: 18px;
  color: #fff;
  line-height: 40px;
  position: relative;
  padding-left: 15px;
}

.alltitle:before {
  position: absolute;
  height: 16px;
  width: 4px;
  background: #49bcf7;
  border-radius: 5px;
  content: "";
  left: 0;
  top: 50%;
  margin-top: -8px;
}

.boxnav {
  height: calc(100% - 40px);
}

.row > li {
  float: left;
  height: 100%;
}

.col-6 {
  width: 50%;
}

.col-3 {
  width: 25%;
}

.col-4 {
  width: 33.33333%;
}

.h100 {
  height: 100%;
}

.tit01 {
  text-align: center;
  color: white;
  font-size: 16px;
  padding: 25px 0 2px 0;
}

.piebox {
  height: calc(100% - 40px);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.piebox:before {
  width: 60px;
  height: 60px;
  content: "";
  border: 1px solid #49bcf7;
  border-radius: 50%;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -30px;
  margin-top: -30px;
  opacity: 0.7;
}

.pie-number {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  position: relative;
  z-index: 1;
}

.sqzs {
  margin-right: 20px;
}

.sqzs p {
  padding: 20px 0 10px 0;
  font-size: 18px;
}

.sqzs h1 {
  height: calc(100% - 65px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  color: #fef000;
  font-weight: normal;
  letter-spacing: 2px;
  font-size: 24px;
  justify-content: center;
  padding-bottom: 5px;
}

.sqzs h1 span {
  font-size: 60px;
  font-family: Impact, Haettenschweiler, "Arial Narrow Bold", sans-serif;
}

table {
  width: 100%;
  text-align: center;
}

table th {
  font-size: 16px;
  background: rgba(0, 0, 0, 0.1);
}

table td {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.6);
}

table th,
table td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 10px 0;
}

@font-face {
  font-family: "DS-DIGIT";
  src: url("../../assets/fonts/DS-DIGIT.TTF");
}
</style>
