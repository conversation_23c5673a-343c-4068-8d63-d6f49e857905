<template>
  <div class="bigdata-template">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button type="primary" icon="el-icon-arrow-left" @click="goBack">
        返回
      </el-button>
    </div>

    <!-- 加载动画 -->
    <div class="loading" v-show="loading">
      <div class="loadbox">
        <img src="~@/assets/images/bigdata/loading.gif" /> 页面加载中...
      </div>
    </div>

    <!-- 头部 -->
    <div class="head">
      <h1>大数据可视化系统数据分析通用模版</h1>
      <div class="time" id="showTime">
        {{ currentTime }}
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="mainbox">
      <ul class="clearfix">
        <!-- 左侧列 -->
        <li>
          <div class="boxall" style="height: calc(58% - .15rem)">
            <div class="alltitle">
              近七日订单数量
            </div>
            <div class="boxnav" id="echarts4">
              <div ref="chart4" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              近七日会员活跃数
            </div>
            <div class="boxnav" id="echarts3">
              <div ref="chart3" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
        </li>

        <!-- 中间列 -->
        <li>
          <div class="boxall" style="height: calc(20% - .15rem)">
            <ul class="row h100 clearfix">
              <li class="col-6">
                <div class="sqzs h100">
                  <p>业绩总览</p>
                  <h1><span>{{ formatNumber(homeData.totalPay) }}</span>万</h1>
                </div>
              </li>
              <li class="col-6">
                <ul class="row h100 clearfix">
                  <li class="col-4">
                    <div class="tit01">
                      今日订单
                    </div>
                    <div class="piebox" id="pe01">
                      <div class="pie-number">{{ homeData.todayOrder }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      今日用户
                    </div>
                    <div class="piebox" id="pe02">
                      <div class="pie-number">{{ homeData.todayUser }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      活跃用户
                    </div>
                    <div class="piebox" id="pe03">
                      <div class="pie-number">{{ homeData.todayActiveUser }}</div>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
          <div class="boxall" style="height: calc(38% - .15rem)">
            <div class="alltitle">
              订单趋势分析
            </div>
            <div class="boxnav" id="echarts1">
              <div ref="chart1" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              交易金额趋势
            </div>
            <div class="boxnav" id="echarts2">
              <div ref="chart2" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
        </li>

        <!-- 右侧列 -->
        <li>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              商品销售排行
            </div>
            <div class="boxnav" id="echarts5">
              <div ref="chart5" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              数据统计表
            </div>
            <div class="boxnav" id="">
              <table border="0" cellspacing="0">
                <tbody>
                <tr>
                  <th></th>
                  <th>订单数</th>
                  <th>增长率</th>
                  <th>用户数</th>
                  <th>金额</th>
                </tr>
                <tr v-for="(item, index) in tableData" :key="index">
                  <th>{{ item.name }}</th>
                  <td>{{ item.orders }}</td>
                  <td>{{ item.growth }}%</td>
                  <td>{{ item.users }}</td>
                  <td>{{ item.amount }}</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="boxall" style="height: calc(33.333% - .15rem)">
            <div class="alltitle">
              用户分布
            </div>
            <div class="boxnav" id="echarts6" style="height:calc(100% - .3rem);">
              <div ref="chart6" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'

export default {
  name: 'BigDataTemplate',
  data() {
    return {
      loading: true,
      currentTime: '',
      homeData: {
        todayOrder: 156,
        todayUser: 89,
        todayActiveUser: 234,
        totalPay: 30500
      },
      tableData: [
        { name: '今日', orders: 8098, growth: 19.80, users: 22, amount: 368 },
        { name: '昨日', orders: 7506, growth: 6.70, users: 22, amount: 339 },
        { name: '本周', orders: 3261, growth: 32.30, users: 10, amount: 325.7 },
        { name: '本月', orders: 1993, growth: 201, users: 10, amount: 199 }
      ],
      charts: {}
    }
  },
  mounted() {
    this.initTime()
    this.initCharts()
    setTimeout(() => {
      this.loading = false
    }, 1000)
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    // 初始化时间显示
    initTime() {
      this.updateTime()
      setInterval(this.updateTime, 1000)
    },
    updateTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      this.currentTime = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
    },
    // 格式化数字
    formatNumber(num) {
      return (num / 10000).toFixed(1)
    },
    // 初始化所有图表
    initCharts() {
      this.$nextTick(() => {
        this.initChart1()
        this.initChart2()
        this.initChart3()
        this.initChart4()
        this.initChart5()
        this.initChart6()
      })
    },
    // 图表1 - 订单趋势分析（混合图表）
    initChart1() {
      this.charts.chart1 = echarts.init(this.$refs.chart1)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          top: '20%',
          right: '50',
          bottom: '20',
          left: '30'
        },
        legend: {
          data: ['订单量', '销售额', '用户数', '订单增长率', '销售增长率', '用户增长率'],
          right: 'center',
          width: '100%',
          textStyle: { color: '#fff' },
          itemWidth: 12,
          itemHeight: 10
        },
        xAxis: [{
          type: 'category',
          data: ['2020', '2021', '2022', '2023'],
          axisLine: { lineStyle: { color: 'rgba(255,255,255,.1)' } },
          axisLabel: { textStyle: { color: 'rgba(255,255,255,.7)', fontSize: '14' } }
        }],
        yAxis: [
          {
            type: 'value',
            name: '单位万',
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              show: true,
              fontSize: 14,
              color: 'rgba(255,255,255,.6)'
            },
            axisLine: {
              min: 0,
              max: 10,
              lineStyle: { color: 'rgba(255,255,255,.1)' }
            }
          },
          {
            type: 'value',
            name: '增速',
            show: true,
            axisLabel: {
              show: true,
              fontSize: 14,
              formatter: '{value} %',
              color: 'rgba(255,255,255,.6)'
            },
            axisTick: { show: false },
            axisLine: { lineStyle: { color: 'rgba(255,255,255,.1)' } },
            splitLine: { show: true, lineStyle: { color: 'rgba(255,255,255,.1)' } }
          }
        ],
        series: [
          {
            name: '订单量',
            type: 'bar',
            data: [36.6, 38.80, 40.84, 41.60],
            barWidth: '15%',
            itemStyle: {
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#8bd46e' },
                  { offset: 1, color: '#09bcb7' }
                ])
              }
            },
            barGap: '0.2'
          },
          {
            name: '销售额',
            type: 'bar',
            data: [14.8, 14.1, 15, 16.30],
            barWidth: '15%',
            itemStyle: {
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#248ff7' },
                  { offset: 1, color: '#6851f1' }
                ])
              }
            },
            barGap: '0.2'
          },
          {
            name: '用户数',
            type: 'bar',
            data: [9.2, 9.1, 9.85, 8.9],
            barWidth: '15%',
            itemStyle: {
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#fccb05' },
                  { offset: 1, color: '#f5804d' }
                ])
              }
            },
            barGap: '0.2'
          },
          {
            name: '订单增长率',
            type: 'line',
            smooth: true,
            yAxisIndex: 1,
            data: [0, 6.01, 5.26, 1.48],
            lineStyle: { normal: { width: 2 } },
            itemStyle: { normal: { color: '#86d370' } }
          },
          {
            name: '销售增长率',
            type: 'line',
            yAxisIndex: 1,
            data: [0, -4.73, 6.38, 8.67],
            lineStyle: { normal: { width: 2 } },
            itemStyle: { normal: { color: '#3496f8' } },
            smooth: true
          },
          {
            name: '用户增长率',
            type: 'line',
            yAxisIndex: 1,
            data: [0, -1.09, 8.24, -9.64],
            lineStyle: { normal: { width: 2 } },
            itemStyle: { normal: { color: '#fbc30d' } },
            smooth: true
          }
        ]
      }
      this.charts.chart1.setOption(option)
    },
    // 图表2 - 交易金额趋势（面积图）
    initChart2() {
      this.charts.chart2 = echarts.init(this.$refs.chart2)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '0',
          top: '30',
          right: '10',
          bottom: '-20',
          containLabel: true
        },
        legend: {
          data: ['线上销售', '线下销售'],
          right: 'center',
          top: 0,
          textStyle: { color: '#fff' },
          itemWidth: 12,
          itemHeight: 10
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            rotate: -90,
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        }],
        yAxis: [{
          type: 'value',
          axisTick: { show: false },
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            formatter: '{value} %',
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          }
        }],
        series: [
          {
            name: '线上销售',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                color: 'rgba(228, 228, 126, 1)',
                width: 2
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(228, 228, 126, .2)' },
                  { offset: 1, color: 'rgba(228, 228, 126, 0)' }
                ], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            },
            itemStyle: {
              normal: {
                color: 'rgba(228, 228, 126, 1)',
                borderColor: 'rgba(228, 228, 126, .1)',
                borderWidth: 12
              }
            },
            data: [12.50, 14.4, 16.1, 14.9, 20.1, 17.2, 17.0, 13.42, 20.12, 18.94, 17.27, 16.10]
          },
          {
            name: '线下销售',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                color: 'rgba(255, 128, 128, 1)',
                width: 2
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(255, 128, 128,.2)' },
                  { offset: 1, color: 'rgba(255, 128, 128, 0)' }
                ], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            },
            itemStyle: {
              normal: {
                color: 'rgba(255, 128, 128, 1)',
                borderColor: 'rgba(255, 128, 128, .1)',
                borderWidth: 12
              }
            },
            data: [-6.4, 0.1, 6.6, 11.2, 42.1, 26.0, 20.2, 18.31, 21.59, 24.42, 34.03, 32.9]
          }
        ]
      }
      this.charts.chart2.setOption(option)
    },
    // 图表3 - 近七日会员活跃数（堆叠柱状图）
    initChart3() {
      this.charts.chart3 = echarts.init(this.$refs.chart3)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {
          data: ['新增会员', '活跃会员', '流失会员'],
          right: 'center',
          top: 0,
          textStyle: { color: '#fff' },
          itemWidth: 12,
          itemHeight: 10
        },
        grid: {
          left: '0',
          right: '20',
          bottom: '0',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,0.3)' }
          },
          axisLabel: {
            formatter: function(value) {
              return value.split('').join('\n')
            },
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 4,
          axisTick: { show: false },
          splitLine: {
            show: true,
            lineStyle: { color: 'rgba(255,255,255,0.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          axisLine: { show: false }
        },
        series: [
          {
            name: '新增会员',
            type: 'bar',
            stack: 'a',
            barWidth: '30',
            barGap: 0,
            itemStyle: {
              normal: { color: '#8bd46e' }
            },
            data: [331, 497, 440, 81, 163, 366, 257]
          },
          {
            name: '活跃会员',
            type: 'bar',
            stack: 'a',
            barWidth: '30',
            barGap: 0,
            itemStyle: {
              normal: {
                color: '#f5804d',
                barBorderRadius: 0
              }
            },
            data: [48, 97, 56, 59, 90, 98, 164]
          },
          {
            name: '流失会员',
            type: 'bar',
            stack: 'a',
            barWidth: '30',
            barGap: 0,
            itemStyle: {
              normal: {
                color: '#248ff7',
                barBorderRadius: 0
              }
            },
            data: [13, 21, 12, 5, 8, 15, 72]
          }
        ]
      }
      this.charts.chart3.setOption(option)
    },
    // 图表4 - 近七日订单数量（柱状图）
    initChart4() {
      this.charts.chart4 = echarts.init(this.$refs.chart4)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          }
        },
        series: [{
          name: '订单数量',
          type: 'bar',
          barWidth: '60%',
          data: [120, 200, 150, 80, 70, 110, 130],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00d386' },
                { offset: 1, color: '#0093dd' }
              ]),
              barBorderRadius: [5, 5, 0, 0]
            }
          }
        }]
      }
      this.charts.chart4.setOption(option)
    },
    // 图表5 - 商品销售排行（横向柱状图）
    initChart5() {
      this.charts.chart5 = echarts.init(this.$refs.chart5)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          }
        },
        yAxis: {
          type: 'category',
          data: ['商品E', '商品D', '商品C', '商品B', '商品A'],
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          }
        },
        series: [{
          name: '销售量',
          type: 'bar',
          data: [50, 80, 120, 150, 200],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                { offset: 0, color: '#fccb05' },
                { offset: 1, color: '#f5804d' }
              ]),
              barBorderRadius: [0, 5, 5, 0]
            }
          }
        }]
      }
      this.charts.chart5.setOption(option)
    },
    // 图表6 - 用户分布（饼图）
    initChart6() {
      this.charts.chart6 = echarts.init(this.$refs.chart6)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          textStyle: { color: '#fff' },
          data: ['新用户', '老用户', 'VIP用户', '普通用户']
        },
        series: [{
          name: '用户分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
              color: '#fff'
            }
          },
          labelLine: { show: false },
          data: [
            { value: 335, name: '新用户', itemStyle: { color: '#8bd46e' } },
            { value: 310, name: '老用户', itemStyle: { color: '#248ff7' } },
            { value: 234, name: 'VIP用户', itemStyle: { color: '#f5804d' } },
            { value: 135, name: '普通用户', itemStyle: { color: '#fccb05' } }
          ]
        }]
      }
      this.charts.chart6.setOption(option)
    }
  }
}
</script>

<style scoped>
@font-face {
  font-family: electronicFont;
  src: url('~@/assets/fonts/DS-DIGIT.TTF');
}

.bigdata-template {
  width: 100%;
  height: 100vh;
  background: #000d4a url('~@/assets/images/bigdata/bg.jpg') center center;
  background-size: cover;
  color: #fff;
  font-size: 0.1rem;
  overflow: hidden;
  position: relative;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
}

.back-button .el-button {
  background: rgba(6, 48, 109, 0.8);
  border-color: #49bcf7;
  color: #fff;
}

.back-button .el-button:hover {
  background: rgba(6, 48, 109, 1);
  border-color: #49bcf7;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: "微软雅黑";
}

li {
  list-style-type: none;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: " ";
}

.clearfix:after {
  clear: both;
}

.loading {
  position: fixed;
  left: 0;
  top: 0;
  font-size: 16px;
  z-index: 100000000;
  width: 100%;
  height: 100%;
  background: #1a1a1c;
  text-align: center;
}

.loadbox {
  position: absolute;
  width: 160px;
  height: 150px;
  color: #324e93;
  left: 50%;
  top: 50%;
  margin-top: -100px;
  margin-left: -75px;
}

.loadbox img {
  margin: 10px auto;
  display: block;
  width: 40px;
}

.head {
  height: 1.05rem;
  background: url('~@/assets/images/bigdata/head_bg.png') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
}

.head h1 {
  color: #fff;
  text-align: center;
  font-size: 0.4rem;
  line-height: 0.8rem;
  letter-spacing: -1px;
}

.time {
  position: absolute;
  right: 0.15rem;
  top: 0;
  line-height: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.3rem;
  padding-right: 0.1rem;
  font-family: electronicFont;
}

.mainbox {
  padding: 0 0.2rem 0rem 0.2rem;
  height: calc(100% - 1.05rem);
}

.mainbox > ul {
  margin-left: -0.1rem;
  margin-right: -0.1rem;
  height: 100%;
}

.mainbox > ul > li {
  float: left;
  padding: 0 0.1rem;
  height: 100%;
  width: 30%;
}

.mainbox > ul > li:nth-child(2) {
  width: 40%;
}

.boxall {
  padding: 0 0.2rem 0.2rem 0.2rem;
  background: rgba(6, 48, 109, 0.5);
  position: relative;
  margin-bottom: 0.15rem;
  z-index: 10;
}

.alltitle {
  font-size: 0.2rem;
  color: #fff;
  line-height: 0.5rem;
  position: relative;
  padding-left: 0.15rem;
}

.alltitle:before {
  position: absolute;
  height: 0.2rem;
  width: 4px;
  background: #49bcf7;
  border-radius: 5px;
  content: "";
  left: 0;
  top: 50%;
  margin-top: -0.1rem;
}

.boxnav {
  height: calc(100% - 0.5rem);
}

.row > li {
  float: left;
  height: 100%;
}

.col-6 {
  width: 50%;
}

.col-4 {
  width: 33.33333%;
}

.h100 {
  height: 100%;
}

.tit01 {
  text-align: center;
  color: white;
  font-size: 0.16rem;
  padding: 0.3rem 0 0.02rem 0;
}

.piebox {
  height: calc(100% - 0.5rem);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.piebox:before {
  width: 0.6rem;
  height: 0.6rem;
  content: "";
  border: 1px solid #49bcf7;
  border-radius: 1rem;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -0.31rem;
  margin-top: -0.31rem;
  opacity: 0.7;
}

.pie-number {
  font-size: 0.24rem;
  color: #49bcf7;
  font-weight: bold;
  z-index: 2;
  position: relative;
}

.sqzs {
  margin-right: 0.2rem;
}

.sqzs p {
  padding: 0.2rem 0 0.1rem 0;
  font-size: 0.22rem;
}

.sqzs h1 {
  height: calc(100% - 0.65rem);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  color: #fef000;
  font-weight: normal;
  letter-spacing: 2px;
  font-size: 0.5rem;
  justify-content: center;
  padding-bottom: 0.05rem;
}

.sqzs h1 span {
  font-size: 0.8rem;
  font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;
}

table {
  width: 100%;
  text-align: center;
}

table th {
  font-size: 0.16rem;
  background: rgba(0, 0, 0, 0.1);
}

table td {
  font-size: 0.16rem;
  color: rgba(255, 255, 255, 0.6);
}

table th,
table td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.1rem 0;
}

/* 响应式字体大小 */
html {
  font-size: calc(100vw / 20);
}

@media screen and (max-width: 1024px) {
  html {
    font-size: calc(100vw / 15);
  }
}
</style>
