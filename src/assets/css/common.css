@charset "utf-8";
/* CSS Document */
* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box
}
*,body {padding:0px;	margin:0px;font-family: "微软雅黑";}
body{ background:#000d4a url(../images/bigdata/bg.jpg) center center; background-size:cover;color:#fff; font-size: 16px; }
li{ list-style-type:none;}
@font-face{font-family:electronicFont;src:url(../fonts/DS-DIGIT.TTF)}
i{ margin:0px; padding:0px; text-indent:0px;}
img{ border:none; max-width: 100%;}
a{ text-decoration:none; color:#399bff;}
a.active,a:focus{ outline:none!important; text-decoration:none;}
ol,ul,p,h1,h2,h3,h4,h5,h6{ padding:0; margin:0}
a:hover{ color:#06c; text-decoration: none!important}

html,body{height: 100%;}
.clearfix:after, .clearfix:before {display: table;content: " "}
.clearfix:after {clear: both}
.pulll_left{float:left;}
.pulll_right{float:right;}
/*谷哥滚动条样式*/
::-webkit-scrollbar {width:5px;height:5px;position:absolute}
::-webkit-scrollbar-thumb {background-color:#5bc0de}
::-webkit-scrollbar-track {background-color:#ddd} 